import { useRef, useState } from 'react'
import { useGSAP } from '@gsap/react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const Navigation = () => {
  const navRef = useRef()
  const [isVisible, setIsVisible] = useState(false)

  const menuItems = [
    { name: "Home", href: "#home" },
    { name: "Services", href: "#services" },
    { name: "Experience", href: "#experience" },
    { name: "Contact", href: "#contact" }
  ]

  useGSAP(() => {
    // Show/hide navigation based on scroll
    ScrollTrigger.create({
      start: "100px top",
      end: "bottom bottom",
      onUpdate: (self) => {
        if (self.direction === 1 && self.progress > 0.1) {
          // Scrolling down
          if (!isVisible) {
            setIsVisible(true)
            gsap.to(navRef.current, {
              y: 0,
              opacity: 1,
              duration: 0.3,
              ease: "power2.out"
            })
          }
        } else if (self.direction === -1 && self.progress < 0.05) {
          // Scrolling up to top
          if (isVisible) {
            setIsVisible(false)
            gsap.to(navRef.current, {
              y: -100,
              opacity: 0,
              duration: 0.3,
              ease: "power2.out"
            })
          }
        }
      }
    })

    // Initial state
    gsap.set(navRef.current, { y: -100, opacity: 0 })

  }, { scope: navRef })

  const handleMenuClick = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <nav ref={navRef} className="navigation">
      <div className="nav-container">
        <div className="nav-logo">
          <div className="logo-circle">FRAJ</div>
        </div>
        
        <ul className="nav-menu">
          {menuItems.map((item, index) => (
            <li key={index} className="nav-item">
              <button 
                onClick={() => handleMenuClick(item.href)}
                className="nav-link"
              >
                {item.name}
              </button>
            </li>
          ))}
        </ul>
        
        <div className="nav-cta">
          <button 
            onClick={() => handleMenuClick('#contact')}
            className="cta-button-small"
          >
            Let's Talk
          </button>
        </div>
      </div>
    </nav>
  )
}

export default Navigation
