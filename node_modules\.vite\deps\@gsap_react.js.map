{"version": 3, "sources": ["../../@gsap/react/src/index.js"], "sourcesContent": ["/*!\n * @gsap/react 2.1.2\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license or for\n * Club GSAP members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\nimport { useEffect, useLayoutEffect, useRef } from \"react\";\nimport gsap from \"gsap\";\n\nlet useIsomorphicLayoutEffect = typeof document !== \"undefined\" ? useLayoutEffect : useEffect,\n    isConfig = value => value && !Array.isArray(value) && typeof(value) === \"object\",\n    emptyArray = [],\n    defaultConfig = {},\n    _gsap = gsap; // accommodates situations where different versions of GSAP may be loaded, so a user can gsap.registerPlugin(useGSAP);\n\nexport const useGSAP = (callback, dependencies = emptyArray) => {\n  let config = defaultConfig;\n  if (isConfig(callback)) {\n    config = callback;\n    callback = null;\n    dependencies = \"dependencies\" in config ? config.dependencies : emptyArray;\n  } else if (isConfig(dependencies)) {\n    config = dependencies;\n    dependencies = \"dependencies\" in config ? config.dependencies : emptyArray;\n  }\n  (callback && typeof callback !== \"function\") && console.warn(\"First parameter must be a function or config object\");\n  const { scope, revertOnUpdate } = config,\n        mounted = useRef(false),\n        context = useRef(_gsap.context(() => { }, scope)),\n        contextSafe = useRef((func) => context.current.add(null, func)),\n        deferCleanup = dependencies && dependencies.length && !revertOnUpdate;\n  deferCleanup && useIsomorphicLayoutEffect(() => {\n    mounted.current = true;\n    return () => context.current.revert();\n  }, emptyArray);\n  useIsomorphicLayoutEffect(() => {\n    callback && context.current.add(callback, scope);\n    if (!deferCleanup || !mounted.current) { // React renders bottom-up, thus there could be hooks with dependencies that run BEFORE the component mounts, thus cleanup wouldn't occur since a hook with an empty dependency Array would only run once the component mounts.\n      return () => context.current.revert();\n    }\n  }, dependencies);\n  return { context: context.current, contextSafe: contextSafe.current };\n};\nuseGSAP.register = core => { _gsap = core; };\nuseGSAP.headless = true; // doesn't require the window to be registered.\n"], "mappings": ";;;;;;;;;;;AAUA,mBAAmD;AAGnD,IAAI,4BAA4B,OAAO,aAAa,cAAc,+BAAkB;AAApF,IACI,WAAW,WAAS,SAAS,CAAC,MAAM,QAAQ,KAAK,KAAK,OAAO,UAAW;AAD5E,IAEI,aAAa,CAAC;AAFlB,IAGI,gBAAgB,CAAC;AAHrB,IAII,QAAQ;AAEL,IAAM,UAAU,CAAC,UAAU,eAAe,eAAe;AAC9D,MAAI,SAAS;AACb,MAAI,SAAS,QAAQ,GAAG;AACtB,aAAS;AACT,eAAW;AACX,mBAAe,kBAAkB,SAAS,OAAO,eAAe;AAAA,EAClE,WAAW,SAAS,YAAY,GAAG;AACjC,aAAS;AACT,mBAAe,kBAAkB,SAAS,OAAO,eAAe;AAAA,EAClE;AACA,EAAC,YAAY,OAAO,aAAa,cAAe,QAAQ,KAAK,qDAAqD;AAClH,QAAM,EAAE,OAAO,eAAe,IAAI,QAC5B,cAAU,qBAAO,KAAK,GACtB,cAAU,qBAAO,MAAM,QAAQ,MAAM;AAAA,EAAE,GAAG,KAAK,CAAC,GAChD,kBAAc,qBAAO,CAAC,SAAS,QAAQ,QAAQ,IAAI,MAAM,IAAI,CAAC,GAC9D,eAAe,gBAAgB,aAAa,UAAU,CAAC;AAC7D,kBAAgB,0BAA0B,MAAM;AAC9C,YAAQ,UAAU;AAClB,WAAO,MAAM,QAAQ,QAAQ,OAAO;AAAA,EACtC,GAAG,UAAU;AACb,4BAA0B,MAAM;AAC9B,gBAAY,QAAQ,QAAQ,IAAI,UAAU,KAAK;AAC/C,QAAI,CAAC,gBAAgB,CAAC,QAAQ,SAAS;AACrC,aAAO,MAAM,QAAQ,QAAQ,OAAO;AAAA,IACtC;AAAA,EACF,GAAG,YAAY;AACf,SAAO,EAAE,SAAS,QAAQ,SAAS,aAAa,YAAY,QAAQ;AACtE;AACA,QAAQ,WAAW,UAAQ;AAAE,UAAQ;AAAM;AAC3C,QAAQ,WAAW;", "names": []}