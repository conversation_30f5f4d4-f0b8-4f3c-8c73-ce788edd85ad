import { useRef } from 'react'
import { useGSAP } from '@gsap/react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const ServicesMenu = () => {
  const menuRef = useRef()
  const titleRef = useRef()

  const services = [
    { name: "ILLUSTRAZIONE", icon: "🎨" },
    { name: "ART DIRECTION", icon: "🎯" },
    { name: "GRAPHIC DESIGN", icon: "✏️" },
    { name: "LIVE PAINTING", icon: "🖌️" },
    { name: "MURALES", icon: "🏢" },
    { name: "WORKSHOP", icon: "👥" },
    { name: "TEAM BUILDING", icon: "🤝" },
    { name: "MOTION GRAPHIC", icon: "🎬" },
    { name: "SHOP", icon: "🛍️" },
    { name: "VIDEO GAMES", icon: "🎮" },
    { name: "PASTA AL POMODORO", icon: "🍝" }
  ]

  useGSAP(() => {
    // Menu title animation
    gsap.fromTo(titleRef.current,
      { opacity: 0, x: -100 },
      {
        opacity: 1,
        x: 0,
        duration: 1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: titleRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    )

    // Service items animation
    const serviceItems = menuRef.current.querySelectorAll('.service-item')
    
    serviceItems.forEach((item, index) => {
      gsap.fromTo(item,
        { opacity: 0, x: -50 },
        {
          opacity: 1,
          x: 0,
          duration: 0.6,
          delay: index * 0.1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: item,
            start: "top 85%",
            end: "bottom 15%",
            toggleActions: "play none none reverse"
          }
        }
      )

      // Hover animations
      const handleMouseEnter = () => {
        gsap.to(item, {
          x: 20,
          backgroundColor: "#8bc34a",
          color: "#ffffff",
          duration: 0.3,
          ease: "power2.out"
        })
      }

      const handleMouseLeave = () => {
        gsap.to(item, {
          x: 0,
          backgroundColor: "transparent",
          color: "#000000",
          duration: 0.3,
          ease: "power2.out"
        })
      }

      item.addEventListener('mouseenter', handleMouseEnter)
      item.addEventListener('mouseleave', handleMouseLeave)

      // Cleanup
      return () => {
        item.removeEventListener('mouseenter', handleMouseEnter)
        item.removeEventListener('mouseleave', handleMouseLeave)
      }
    })

  }, { scope: menuRef })

  return (
    <section ref={menuRef} className="section services-section">
      <div className="container">
        <div className="services-content">
          <h2 ref={titleRef} className="title-medium services-title">
            IL MENÙ DI FRAJ
          </h2>
          
          <ul className="services-menu">
            {services.map((service, index) => (
              <li key={index} className="service-item">
                <div className="service-icon">{service.icon}</div>
                <div className="service-text title-small">
                  {service.name}
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  )
}

export default ServicesMenu
