import { useRef, useEffect, useState } from 'react'
import { useGSAP } from '@gsap/react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { TextPlugin } from 'gsap/TextPlugin'
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin'
import './styles/main.scss'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, TextPlugin, MorphSVGPlugin)

// Components
import LoadingScreen from './components/LoadingScreen'
import Navigation from './components/Navigation'
import Header from './components/Header'
import ServicesMenu from './components/ServicesMenu'
import Experience from './components/Experience'
import Contact from './components/Contact'

function App() {
  const appRef = useRef()
  const [isLoading, setIsLoading] = useState(true)

  useGSAP(() => {
    // Set up smooth scrolling
    gsap.registerPlugin(ScrollTrigger)

    // Create a master timeline for page load
    const masterTL = gsap.timeline()

    // Initial page load animation with stagger
    masterTL.fromTo('.section',
      { opacity: 0, y: 100, scale: 0.95 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1.2,
        stagger: 0.3,
        ease: "power3.out"
      }
    )

    // Add a subtle parallax effect to sections
    gsap.utils.toArray('.section').forEach((section, i) => {
      gsap.to(section, {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
          trigger: section,
          start: "bottom bottom",
          end: "top top",
          scrub: true
        }
      })
    })

    // Add mouse follower effect
    const cursor = document.createElement('div')
    cursor.className = 'custom-cursor'
    document.body.appendChild(cursor)

    const moveCursor = (e) => {
      gsap.to(cursor, {
        x: e.clientX,
        y: e.clientY,
        duration: 0.1,
        ease: "power2.out"
      })
    }

    document.addEventListener('mousemove', moveCursor)

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', moveCursor)
      if (cursor.parentNode) {
        cursor.parentNode.removeChild(cursor)
      }
    }

  }, { scope: appRef })

  const handleLoadingComplete = () => {
    setIsLoading(false)
  }

  return (
    <>
      {isLoading && <LoadingScreen onComplete={handleLoadingComplete} />}
      <div ref={appRef} className="app">
        <Navigation />
        <div id="home">
          <Header />
        </div>
        <div id="services">
          <ServicesMenu />
        </div>
        <div id="experience">
          <Experience />
        </div>
        <div id="contact">
          <Contact />
        </div>
      </div>
    </>
  )
}

export default App
