import { useRef } from 'react'
import { useGSAP } from '@gsap/react'
import gsap from 'gsap'

const Header = () => {
  const headerRef = useRef()
  const titleRef = useRef()
  const subtitleRef = useRef()
  const characterRef = useRef()

  useGSAP(() => {
    const tl = gsap.timeline({ delay: 0.5 })

    // Split title text for letter-by-letter animation
    const titleText = titleRef.current.textContent
    titleRef.current.innerHTML = titleText
      .split('')
      .map(char => char === ' ' ? ' ' : `<span class="char">${char}</span>`)
      .join('')

    const chars = titleRef.current.querySelectorAll('.char')

    // Character illustration animation with bounce
    tl.fromTo(characterRef.current,
      { scale: 0, rotation: -20, y: 100 },
      { scale: 1, rotation: 0, y: 0, duration: 1.5, ease: "elastic.out(1, 0.5)" }
    )

    // Title animation with letter-by-letter effect
    .fromTo(chars,
      { opacity: 0, y: 100, rotation: 10 },
      {
        opacity: 1,
        y: 0,
        rotation: 0,
        duration: 0.8,
        stagger: 0.05,
        ease: "back.out(1.7)"
      }, "-=1"
    )

    // Subtitle animation with typewriter effect
    .fromTo(subtitleRef.current,
      { opacity: 0 },
      {
        opacity: 1,
        duration: 0.1,
        ease: "none"
      }, "-=0.3"
    )
    .to(subtitleRef.current, {
      text: "CIAO CHE UN MONDO GUSTO IN CUI C'È SEMPRE POSTO PER TUTTI",
      duration: 2,
      ease: "none"
    })

    // Floating animation for character
    gsap.to(characterRef.current, {
      y: -15,
      rotation: 2,
      duration: 3,
      repeat: -1,
      yoyo: true,
      ease: "power2.inOut",
      delay: 2
    })

    // Add hover effect to character
    const handleCharacterHover = () => {
      gsap.to(characterRef.current, {
        scale: 1.1,
        rotation: 5,
        duration: 0.3,
        ease: "power2.out"
      })
    }

    const handleCharacterLeave = () => {
      gsap.to(characterRef.current, {
        scale: 1,
        rotation: 0,
        duration: 0.3,
        ease: "power2.out"
      })
    }

    characterRef.current.addEventListener('mouseenter', handleCharacterHover)
    characterRef.current.addEventListener('mouseleave', handleCharacterLeave)

    // Cleanup
    return () => {
      if (characterRef.current) {
        characterRef.current.removeEventListener('mouseenter', handleCharacterHover)
        characterRef.current.removeEventListener('mouseleave', handleCharacterLeave)
      }
    }

  }, { scope: headerRef })

  return (
    <section ref={headerRef} className="section header-section">
      <div className="container">
        <div className="header-content">
          <div className="header-text">
            <h1 ref={titleRef} className="title-large main-title">
              CHI È FRAJ
            </h1>
            <p ref={subtitleRef} className="body-text subtitle">
              CIAO CHE UN MONDO GUSTO IN CUI C'È SEMPRE POSTO PER TUTTI
            </p>
            <div className="intro-text">
              <p className="body-text">
                UI Developer with 20 years of experience crafting award-winning digital experiences. 
                Multiple Awwwards recipient specializing in creative web development and interactive design.
              </p>
            </div>
          </div>
          
          <div ref={characterRef} className="character-illustration">
            {/* Character illustration placeholder - in real project, this would be an SVG or image */}
            <div className="character-placeholder">
              <div className="character-face">
                <div className="character-eyes">
                  <div className="eye left-eye"></div>
                  <div className="eye right-eye"></div>
                </div>
                <div className="character-mouth"></div>
              </div>
              <div className="character-body"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Header
