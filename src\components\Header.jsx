import { useRef } from 'react'
import { useGSAP } from '@gsap/react'
import gsap from 'gsap'

const Header = () => {
  const headerRef = useRef()
  const titleRef = useRef()
  const subtitleRef = useRef()
  const characterRef = useRef()

  useGSAP(() => {
    const tl = gsap.timeline({ delay: 0.5 })

    // Split title text for letter-by-letter animation
    const titleText = titleRef.current.textContent
    titleRef.current.innerHTML = titleText
      .split('')
      .map(char => char === ' ' ? ' ' : `<span class="char">${char}</span>`)
      .join('')

    const chars = titleRef.current.querySelectorAll('.char')

    // Character illustration animation with bounce
    tl.fromTo(characterRef.current,
      { scale: 0, rotation: -20, y: 100 },
      { scale: 1, rotation: 0, y: 0, duration: 1.5, ease: "elastic.out(1, 0.5)" }
    )

    // Title animation with letter-by-letter effect
    .fromTo(chars,
      { opacity: 0, y: 100, rotation: 10 },
      {
        opacity: 1,
        y: 0,
        rotation: 0,
        duration: 0.8,
        stagger: 0.05,
        ease: "back.out(1.7)"
      }, "-=1"
    )

    // Subtitle animation with typewriter effect
    .fromTo(subtitleRef.current,
      { opacity: 0 },
      {
        opacity: 1,
        duration: 0.1,
        ease: "none"
      }, "-=0.3"
    )
    .to(subtitleRef.current, {
      text: "CIAO CHE UN MONDO GUSTO IN CUI C'È SEMPRE POSTO PER TUTTI",
      duration: 2,
      ease: "none"
    })

    // Floating animation for character
    gsap.to(characterRef.current, {
      y: -15,
      rotation: 2,
      duration: 3,
      repeat: -1,
      yoyo: true,
      ease: "power2.inOut",
      delay: 2
    })

    // Add hover effect to character
    const handleCharacterHover = () => {
      gsap.to(characterRef.current, {
        scale: 1.1,
        rotation: 5,
        duration: 0.3,
        ease: "power2.out"
      })
    }

    const handleCharacterLeave = () => {
      gsap.to(characterRef.current, {
        scale: 1,
        rotation: 0,
        duration: 0.3,
        ease: "power2.out"
      })
    }

    characterRef.current.addEventListener('mouseenter', handleCharacterHover)
    characterRef.current.addEventListener('mouseleave', handleCharacterLeave)

    // Cleanup
    return () => {
      if (characterRef.current) {
        characterRef.current.removeEventListener('mouseenter', handleCharacterHover)
        characterRef.current.removeEventListener('mouseleave', handleCharacterLeave)
      }
    }

  }, { scope: headerRef })

  return (
    <section ref={headerRef} className="section header-section">
      <div className="container">
        <div className="header-content">
          <div className="header-text">
            <div className="title-with-logo">
              <h1 ref={titleRef} className="title-large main-title">
                CHI È FRAJ
              </h1>
              <div className="logo-circle">
                <div className="logo-inner">FRAJ</div>
              </div>
            </div>

            <p ref={subtitleRef} className="body-text subtitle">

            </p>

            <div className="description-text">
              <p className="body-text">
                CIAO CHE UN MONDO GUSTO IN CUI C'È SEMPRE POSTO PER TUTTI
              </p>
              <p className="body-text experience-text">
                UI Developer • 20 Years Experience • Multiple Awwwards Winner
              </p>
            </div>
          </div>

          <div ref={characterRef} className="portrait-image">
            <div className="portrait-placeholder">
              <div className="portrait-frame">
                <div className="portrait-photo">
                  {/* This would be replaced with actual portrait image */}
                  <div className="photo-placeholder">
                    <div className="face-silhouette"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Header
