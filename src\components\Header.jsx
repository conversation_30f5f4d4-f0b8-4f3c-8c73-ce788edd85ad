import { useRef } from 'react'
import { useGSAP } from '@gsap/react'
import gsap from 'gsap'

const Header = () => {
  const headerRef = useRef()
  const titleRef = useRef()
  const subtitleRef = useRef()
  const characterRef = useRef()

  useGSAP(() => {
    const tl = gsap.timeline({ delay: 0.5 })

    // Split title text for letter-by-letter animation
    const titleText = titleRef.current.textContent
    titleRef.current.innerHTML = titleText
      .split('')
      .map(char => char === ' ' ? ' ' : `<span class="char">${char}</span>`)
      .join('')

    const chars = titleRef.current.querySelectorAll('.char')

    // Character illustration animation with bounce
    tl.fromTo(characterRef.current,
      { scale: 0, rotation: -20, y: 100 },
      { scale: 1, rotation: 0, y: 0, duration: 1.5, ease: "elastic.out(1, 0.5)" }
    )

    // Title animation with letter-by-letter effect
    .fromTo(chars,
      { opacity: 0, y: 100, rotation: 10 },
      {
        opacity: 1,
        y: 0,
        rotation: 0,
        duration: 0.8,
        stagger: 0.05,
        ease: "back.out(1.7)"
      }, "-=1"
    )

    // Subtitle animation with typewriter effect
    .fromTo(subtitleRef.current,
      { opacity: 0 },
      {
        opacity: 1,
        duration: 0.1,
        ease: "none"
      }, "-=0.3"
    )
    .to(subtitleRef.current, {
      text: "CIAO CHE UN MONDO GUSTO IN CUI C'È SEMPRE POSTO PER TUTTI",
      duration: 2,
      ease: "none"
    })

    // Floating animation for character
    gsap.to(characterRef.current, {
      y: -15,
      rotation: 2,
      duration: 3,
      repeat: -1,
      yoyo: true,
      ease: "power2.inOut",
      delay: 2
    })

    // Add hover effect to character
    const handleCharacterHover = () => {
      gsap.to(characterRef.current, {
        scale: 1.1,
        rotation: 5,
        duration: 0.3,
        ease: "power2.out"
      })
    }

    const handleCharacterLeave = () => {
      gsap.to(characterRef.current, {
        scale: 1,
        rotation: 0,
        duration: 0.3,
        ease: "power2.out"
      })
    }

    characterRef.current.addEventListener('mouseenter', handleCharacterHover)
    characterRef.current.addEventListener('mouseleave', handleCharacterLeave)

    // Cleanup
    return () => {
      if (characterRef.current) {
        characterRef.current.removeEventListener('mouseenter', handleCharacterHover)
        characterRef.current.removeEventListener('mouseleave', handleCharacterLeave)
      }
    }

  }, { scope: headerRef })

  return (
    <section ref={headerRef} className="section header-section">
      <div className="container">
        <div className="header-content">
          <div className="header-text">
            <div className="title-with-logo">
              <h1 ref={titleRef} className="main-title">
                CHI È FRAJ
              </h1>
              <div className="logo-circle">
                <span className="logo-symbol">🌟</span>
              </div>
            </div>

            <div className="description-text">
              <p className="body-text">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
              </p>
              <p className="subtitle-text">
                CIAO CHE UN MONDO GUSTO IN CUI C'È SEMPRE POSTO PER TUTTI
              </p>
            </div>
          </div>

          <div ref={characterRef} className="portrait-section">
            <div className="portrait-frame">
              <img
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=500&fit=crop&crop=face"
                alt="Portrait"
                className="portrait-image"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Header
