{"hash": "be83be2f", "configHash": "5ac7debf", "lockfileHash": "712bf0b5", "browserHash": "af56e0c4", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "4e80e0dc", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "3687d479", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "f1459eb9", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "37149933", "needsInterop": true}, "@gsap/react": {"src": "../../@gsap/react/src/index.js", "file": "@gsap_react.js", "fileHash": "5d934b17", "needsInterop": false}, "gsap": {"src": "../../gsap/index.js", "file": "gsap.js", "fileHash": "a3e81a51", "needsInterop": false}, "gsap/ScrollTrigger": {"src": "../../gsap/ScrollTrigger.js", "file": "gsap_ScrollTrigger.js", "fileHash": "58cc001e", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "81169110", "needsInterop": true}}, "chunks": {"chunk-UGNHNF6M": {"file": "chunk-UGNHNF6M.js"}, "chunk-E7Q22A2S": {"file": "chunk-E7Q22A2S.js"}, "chunk-6P6Q65E3": {"file": "chunk-6P6Q65E3.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}