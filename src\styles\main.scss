// Reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: '<PERSON><PERSON>', '<PERSON>', sans-serif;
  background-color: #f5f5f5;
  overflow-x: hidden;
  cursor: none;
}

// Custom cursor
.custom-cursor {
  position: fixed;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background-color: var(--accent-green);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 2px solid var(--accent-green);
    border-radius: 50%;
    opacity: 0.3;
  }
}

// Typography
.title-large {
  font-family: '<PERSON><PERSON> Neue', sans-serif;
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 400;
  line-height: 0.9;
  letter-spacing: -0.02em;
}

.title-medium {
  font-family: '<PERSON><PERSON>eue', sans-serif;
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 400;
  line-height: 0.9;
  letter-spacing: -0.01em;
}

.title-small {
  font-family: 'Oswald', sans-serif;
  font-size: clamp(1.2rem, 3vw, 2rem);
  font-weight: 600;
  line-height: 1.1;
  text-transform: uppercase;
}

.body-text {
  font-family: 'Oswald', sans-serif;
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  font-weight: 300;
  line-height: 1.4;
}

// Colors
:root {
  --primary-black: #000000;
  --primary-white: #ffffff;
  --accent-green: #8bc34a;
  --background-light: #f5f5f5;
  --text-dark: #333333;
}

// Layout
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  
  @media (max-width: 768px) {
    padding: 0 1rem;
  }
}

.section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

// Header Section
.header-section {
  background-color: var(--background-light);
  padding: 4rem 0;

  .header-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 4rem;
    align-items: center;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: 2rem;
    }
  }

  .header-text {
    .main-title {
      color: var(--primary-black);
      margin-bottom: 1rem;
    }

    .subtitle {
      color: var(--text-dark);
      margin-bottom: 2rem;
      max-width: 600px;
      font-weight: 500;
    }

    .intro-text {
      margin-top: 2rem;
      max-width: 500px;
    }
  }

  .character-illustration {
    width: 300px;
    height: 400px;
    background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);

    @media (max-width: 1024px) {
      margin: 0 auto;
    }

    .character-placeholder {
      .character-face {
        width: 120px;
        height: 120px;
        background-color: #ffdbac;
        border-radius: 50%;
        position: relative;
        margin-bottom: 2rem;

        .character-eyes {
          display: flex;
          gap: 20px;
          position: absolute;
          top: 40px;
          left: 50%;
          transform: translateX(-50%);

          .eye {
            width: 15px;
            height: 15px;
            background-color: var(--primary-black);
            border-radius: 50%;
          }
        }

        .character-mouth {
          position: absolute;
          bottom: 30px;
          left: 50%;
          transform: translateX(-50%);
          width: 30px;
          height: 15px;
          border: 3px solid var(--primary-black);
          border-top: none;
          border-radius: 0 0 30px 30px;
        }
      }

      .character-body {
        width: 80px;
        height: 100px;
        background-color: var(--accent-green);
        border-radius: 40px 40px 20px 20px;
      }
    }
  }
}

// Services Menu Section
.services-section {
  background-color: var(--primary-white);
  padding: 4rem 0;

  .services-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .services-title {
    color: var(--primary-black);
    margin-bottom: 3rem;
    text-align: center;
  }

  .services-menu {
    list-style: none;

    .service-item {
      border-bottom: 3px solid var(--primary-black);
      padding: 2rem 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:first-child {
        border-top: 3px solid var(--primary-black);
      }

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background-color: var(--accent-green);
        transition: left 0.3s ease;
        z-index: 1;
      }

      &:hover::before {
        left: 0;
      }

      .service-text {
        color: var(--primary-black);
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
        z-index: 2;
        transition: color 0.3s ease;

        &::before {
          content: "→";
          font-size: 2rem;
          opacity: 0;
          transition: opacity 0.3s ease;
          margin-right: 1rem;
        }
      }

      &:hover .service-text {
        color: var(--primary-white);

        &::before {
          opacity: 1;
        }
      }
    }
  }
}

// Experience Section
.experience-section {
  background-color: var(--primary-black);
  color: var(--primary-white);
  padding: 6rem 0;

  .experience-content {
    text-align: center;

    .experience-title {
      margin-bottom: 1rem;
      color: var(--primary-white);
    }

    .experience-subtitle {
      margin-bottom: 4rem;
      color: var(--accent-green);
    }

    .awards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin-bottom: 4rem;

      .award-item {
        padding: 2rem 1rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          border-color: var(--accent-green);
          background: rgba(139, 195, 74, 0.1);
          transform: translateY(-5px);
        }

        .award-logo {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .award-name {
          color: var(--primary-white);
          margin-bottom: 0.5rem;
        }

        .award-count {
          color: var(--accent-green);
          font-weight: 600;
        }
      }
    }

    .clients-section {
      margin-top: 4rem;

      .clients-title {
        margin-bottom: 2rem;
        color: var(--primary-white);
      }

      .clients-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .client-item {
          padding: 1rem;
          color: rgba(255, 255, 255, 0.7);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            color: var(--primary-white);
            border-color: var(--accent-green);
          }
        }
      }
    }
  }
}

// Contact Section
.contact-section {
  background-color: var(--primary-white);
  position: relative;
  padding: 6rem 0;
  min-height: 100vh;

  .drip-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 150px;
    background: var(--primary-black);

    .drip-drops {
      position: absolute;
      bottom: -50px;
      left: 0;
      right: 0;
      height: 100px;

      .drip-drop {
        position: absolute;
        background-color: var(--primary-black);
        border-radius: 0 0 50px 50px;

        &.drip-1 {
          left: 10%;
          width: 60px;
          height: 80px;
          animation: drip 3s ease-in-out infinite;
        }

        &.drip-2 {
          left: 25%;
          width: 40px;
          height: 60px;
          animation: drip 3s ease-in-out infinite 0.5s;
        }

        &.drip-3 {
          left: 50%;
          width: 80px;
          height: 100px;
          animation: drip 3s ease-in-out infinite 1s;
        }

        &.drip-4 {
          left: 75%;
          width: 50px;
          height: 70px;
          animation: drip 3s ease-in-out infinite 1.5s;
        }

        &.drip-5 {
          left: 90%;
          width: 35px;
          height: 50px;
          animation: drip 3s ease-in-out infinite 2s;
        }
      }
    }
  }

  .contact-content {
    text-align: center;
    padding-top: 8rem;

    .contact-logo {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 3rem;

      .logo-circle {
        width: 120px;
        height: 120px;
        background-color: var(--accent-green);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        box-shadow: 0 10px 30px rgba(139, 195, 74, 0.3);

        .logo-character {
          font-family: 'Bebas Neue', sans-serif;
          font-size: 2rem;
          color: var(--primary-white);
          font-weight: 700;
        }
      }

      .logo-text {
        text-align: center;

        .title-small {
          color: var(--primary-black);
          line-height: 1;
        }
      }
    }

    .contact-title {
      margin-bottom: 3rem;
      color: var(--primary-black);

      .char {
        display: inline-block;
      }
    }

    .contact-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;

      .contact-item {
        text-align: center;
        padding: 1.5rem;

        .contact-label {
          color: var(--text-dark);
          margin-bottom: 0.5rem;
          text-transform: uppercase;
          letter-spacing: 0.1em;
        }

        .contact-value {
          color: var(--primary-black);
        }

        .contact-socials {
          display: flex;
          justify-content: center;
          gap: 1rem;
          flex-wrap: wrap;

          .social-link {
            color: var(--primary-black);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 2px solid var(--primary-black);
            border-radius: 25px;
            transition: all 0.3s ease;

            &:hover {
              background-color: var(--accent-green);
              border-color: var(--accent-green);
              color: var(--primary-white);
              transform: translateY(-2px);
            }
          }
        }
      }
    }

    .contact-cta {
      .cta-button {
        background-color: var(--primary-black);
        color: var(--primary-white);
        border: none;
        padding: 1.5rem 3rem;
        border-radius: 50px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Bebas Neue', sans-serif;

        &:hover {
          background-color: var(--accent-green);
          transform: translateY(-3px);
          box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
      }
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes drip {
  0% {
    transform: scaleY(0);
  }
  50% {
    transform: scaleY(1);
  }
  100% {
    transform: scaleY(0.8);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

// Responsive Design
@media (max-width: 768px) {
  .title-large {
    font-size: clamp(2.5rem, 6vw, 6rem);
  }

  .title-medium {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }

  .header-section .header-content {
    gap: 2rem;
  }

  .character-illustration {
    width: 250px !important;
    height: 320px !important;
  }

  .services-section .service-item {
    padding: 1.5rem 0.5rem;
  }

  .experience-section .awards-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .contact-section .contact-info {
    grid-template-columns: 1fr;
  }
}
