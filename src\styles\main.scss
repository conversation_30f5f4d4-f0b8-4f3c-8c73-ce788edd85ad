// Reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: '<PERSON><PERSON>', '<PERSON>', sans-serif;
  background-color: #f5f5f5;
  overflow-x: hidden;
  cursor: none;
  line-height: 1.4;
}

// Navigation
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  .nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 768px) {
      padding: 1rem;
    }
  }

  .nav-logo {
    .logo-circle {
      width: 50px;
      height: 50px;
      background-color: var(--accent-green);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: '<PERSON><PERSON>', sans-serif;
      font-size: 1rem;
      color: var(--primary-white);
      font-weight: 700;
      cursor: pointer;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  .nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;

    @media (max-width: 768px) {
      display: none;
    }

    .nav-item {
      .nav-link {
        background: none;
        border: none;
        font-family: 'Oswald', sans-serif;
        font-size: 1rem;
        font-weight: 500;
        color: var(--primary-black);
        cursor: pointer;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.05em;

        &:hover {
          background-color: var(--accent-green);
          color: var(--primary-white);
          transform: translateY(-2px);
        }
      }
    }
  }

  .nav-cta {
    .cta-button-small {
      background-color: var(--primary-black);
      color: var(--primary-white);
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 25px;
      font-family: 'Oswald', sans-serif;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.05em;

      &:hover {
        background-color: var(--accent-green);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
      }
    }
  }
}

// Custom cursor
.custom-cursor {
  position: fixed;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background-color: var(--accent-green);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 2px solid var(--accent-green);
    border-radius: 50%;
    opacity: 0.3;
  }
}

// Typography
.title-large {
  font-family: 'Bebas Neue', sans-serif;
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 400;
  line-height: 0.9;
  letter-spacing: -0.02em;
}

.title-medium {
  font-family: 'Bebas Neue', sans-serif;
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 400;
  line-height: 0.9;
  letter-spacing: -0.01em;
}

.title-small {
  font-family: 'Oswald', sans-serif;
  font-size: clamp(1.2rem, 3vw, 2rem);
  font-weight: 600;
  line-height: 1.1;
  text-transform: uppercase;
}

.body-text {
  font-family: 'Oswald', sans-serif;
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  font-weight: 300;
  line-height: 1.4;
}

// Colors
:root {
  --primary-black: #000000;
  --primary-white: #ffffff;
  --accent-green: #8bc34a;
  --background-light: #f5f5f5;
  --text-dark: #333333;
}

// Layout
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  
  @media (max-width: 768px) {
    padding: 0 1rem;
  }
}

.section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

// Header Section
.header-section {
  background-color: var(--background-light);
  padding: 6rem 0;
  min-height: 100vh;
  display: flex;
  align-items: center;

  .header-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 6rem;
    align-items: center;
    width: 100%;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: 3rem;
    }
  }

  .header-text {
    .title-with-logo {
      display: flex;
      align-items: center;
      gap: 2rem;
      margin-bottom: 2rem;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 1rem;
      }

      .main-title {
        color: var(--primary-black);
        margin: 0;

        .char {
          display: inline-block;
        }
      }

      .logo-circle {
        width: 80px;
        height: 80px;
        background-color: var(--accent-green);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        box-shadow: 0 10px 30px rgba(139, 195, 74, 0.3);

        .logo-inner {
          font-family: 'Bebas Neue', sans-serif;
          font-size: 1.2rem;
          color: var(--primary-white);
          font-weight: 700;
          letter-spacing: 0.1em;
        }
      }
    }

    .subtitle {
      color: var(--text-dark);
      margin-bottom: 2rem;
      max-width: 600px;
      font-weight: 500;
      min-height: 1.5rem;
    }

    .description-text {
      max-width: 600px;

      .body-text {
        margin-bottom: 1rem;
        line-height: 1.6;

        &.experience-text {
          font-weight: 600;
          color: var(--primary-black);
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      }
    }
  }

  .portrait-image {
    justify-self: end;

    @media (max-width: 1024px) {
      justify-self: center;
    }

    .portrait-placeholder {
      width: 300px;
      height: 400px;
      position: relative;

      @media (max-width: 768px) {
        width: 250px;
        height: 320px;
      }

      .portrait-frame {
        width: 100%;
        height: 100%;
        background-color: var(--primary-white);
        border-radius: 20px;
        padding: 20px;
        box-shadow: 0 20px 60px rgba(0,0,0,0.15);
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent 30%, rgba(139, 195, 74, 0.1) 50%, transparent 70%);
          pointer-events: none;
        }

        .portrait-photo {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #f8f8f8, #e8e8e8);
          border-radius: 15px;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          overflow: hidden;

          .photo-placeholder {
            width: 80%;
            height: 80%;
            position: relative;

            .face-silhouette {
              width: 100%;
              height: 100%;
              background: linear-gradient(135deg, #d0d0d0, #b0b0b0);
              border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
              position: relative;

              &::before {
                content: "";
                position: absolute;
                top: 30%;
                left: 50%;
                transform: translateX(-50%);
                width: 60%;
                height: 40%;
                background: linear-gradient(135deg, #a0a0a0, #808080);
                border-radius: 50%;
              }

              &::after {
                content: "";
                position: absolute;
                bottom: 20%;
                left: 50%;
                transform: translateX(-50%);
                width: 40%;
                height: 30%;
                background: linear-gradient(135deg, #909090, #707070);
                border-radius: 50% 50% 50% 50% / 40% 40% 60% 60%;
              }
            }
          }
        }
      }
    }
  }
}

// Services Menu Section
.services-section {
  background-color: var(--primary-white);
  padding: 6rem 0;
  min-height: 100vh;
  display: flex;
  align-items: center;

  .services-content {
    max-width: 600px;
    margin: 0 auto;
    width: 100%;
  }

  .services-title {
    color: var(--primary-black);
    margin-bottom: 4rem;
    text-align: center;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: -1rem;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background-color: var(--accent-green);
      border-radius: 2px;
    }
  }

  .services-menu {
    list-style: none;

    .service-item {
      border-bottom: 2px solid #e0e0e0;
      padding: 1.5rem 0;
      cursor: pointer;
      transition: all 0.4s ease;
      position: relative;
      display: flex;
      align-items: center;
      gap: 2rem;

      &:first-child {
        border-top: 2px solid #e0e0e0;
      }

      &:hover {
        border-color: var(--accent-green);
        transform: translateX(10px);

        .service-icon {
          transform: scale(1.2) rotate(10deg);
        }

        .service-text {
          color: var(--accent-green);
        }
      }

      .service-icon {
        font-size: 2rem;
        transition: all 0.3s ease;
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f8f8;
        border-radius: 50%;
        border: 2px solid transparent;
      }

      .service-text {
        color: var(--primary-black);
        transition: all 0.3s ease;
        font-weight: 600;
        letter-spacing: 0.05em;
      }

      &:hover .service-icon {
        background-color: var(--accent-green);
        border-color: var(--accent-green);
        color: var(--primary-white);
      }
    }
  }
}

// Experience Section
.experience-section {
  background-color: var(--primary-black);
  color: var(--primary-white);
  padding: 8rem 0;
  min-height: 100vh;
  display: flex;
  align-items: center;

  .experience-content {
    text-align: center;

    .experience-title {
      margin-bottom: 3rem;
      color: var(--primary-white);
    }

    .experience-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 3rem;
      margin-bottom: 4rem;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .stat-item {
        .stat-number {
          color: var(--accent-green);
          margin-bottom: 0.5rem;
          font-size: clamp(3rem, 6vw, 5rem);
        }

        .stat-label {
          color: rgba(255, 255, 255, 0.8);
          text-transform: uppercase;
          letter-spacing: 0.1em;
        }
      }
    }

    .awards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin-bottom: 4rem;

      .award-item {
        padding: 2rem 1rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.05);
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          border-color: var(--accent-green);
          background: rgba(139, 195, 74, 0.1);
          transform: translateY(-5px);
        }

        .award-logo {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .award-name {
          color: var(--primary-white);
          margin-bottom: 0.5rem;
        }

        .award-count {
          color: var(--accent-green);
          font-weight: 600;
        }
      }
    }

    .clients-section {
      margin-top: 4rem;

      .clients-title {
        margin-bottom: 2rem;
        color: var(--primary-white);
      }

      .clients-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;

        .client-item {
          padding: 1rem;
          color: rgba(255, 255, 255, 0.7);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            color: var(--primary-white);
            border-color: var(--accent-green);
          }
        }
      }
    }
  }
}

// Contact Section
.contact-section {
  background-color: var(--primary-white);
  position: relative;
  padding: 6rem 0;
  min-height: 100vh;

  .drip-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    background: var(--primary-black);

    &::before {
      content: "";
      position: absolute;
      bottom: -100px;
      left: 0;
      right: 0;
      height: 150px;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M0,0 C150,100 350,0 500,50 C650,100 850,0 1000,50 C1150,100 1200,50 1200,50 L1200,120 L0,120 Z' fill='%23000000'/%3E%3C/svg%3E") no-repeat center bottom;
      background-size: cover;
    }

    .drip-drops {
      position: absolute;
      bottom: -120px;
      left: 0;
      right: 0;
      height: 150px;

      .drip-drop {
        position: absolute;
        background-color: var(--primary-black);

        &.drip-1 {
          left: 15%;
          width: 40px;
          height: 120px;
          border-radius: 20px 20px 50px 50px;
          animation: drip 4s ease-in-out infinite;
        }

        &.drip-2 {
          left: 30%;
          width: 25px;
          height: 80px;
          border-radius: 12px 12px 30px 30px;
          animation: drip 4s ease-in-out infinite 0.8s;
        }

        &.drip-3 {
          left: 50%;
          width: 60px;
          height: 140px;
          border-radius: 30px 30px 60px 60px;
          animation: drip 4s ease-in-out infinite 1.2s;
        }

        &.drip-4 {
          left: 70%;
          width: 35px;
          height: 100px;
          border-radius: 17px 17px 40px 40px;
          animation: drip 4s ease-in-out infinite 1.8s;
        }

        &.drip-5 {
          left: 85%;
          width: 20px;
          height: 60px;
          border-radius: 10px 10px 25px 25px;
          animation: drip 4s ease-in-out infinite 2.4s;
        }
      }
    }
  }

  .contact-content {
    text-align: center;
    padding-top: 8rem;

    .contact-logo {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 3rem;

      .logo-circle {
        width: 120px;
        height: 120px;
        background-color: var(--accent-green);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        box-shadow: 0 10px 30px rgba(139, 195, 74, 0.3);

        .logo-character {
          font-family: 'Bebas Neue', sans-serif;
          font-size: 2rem;
          color: var(--primary-white);
          font-weight: 700;
        }
      }

      .logo-text {
        text-align: center;

        .title-small {
          color: var(--primary-black);
          line-height: 1;
        }
      }
    }

    .contact-title {
      margin-bottom: 3rem;
      color: var(--primary-black);

      .char {
        display: inline-block;
      }
    }

    .contact-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;

      .contact-item {
        text-align: center;
        padding: 1.5rem;

        .contact-label {
          color: var(--text-dark);
          margin-bottom: 0.5rem;
          text-transform: uppercase;
          letter-spacing: 0.1em;
        }

        .contact-value {
          color: var(--primary-black);
        }

        .contact-socials {
          display: flex;
          justify-content: center;
          gap: 1rem;
          flex-wrap: wrap;

          .social-link {
            color: var(--primary-black);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border: 2px solid var(--primary-black);
            border-radius: 25px;
            transition: all 0.3s ease;

            &:hover {
              background-color: var(--accent-green);
              border-color: var(--accent-green);
              color: var(--primary-white);
              transform: translateY(-2px);
            }
          }
        }
      }
    }

    .contact-cta {
      .cta-button {
        background-color: var(--primary-black);
        color: var(--primary-white);
        border: none;
        padding: 1.5rem 3rem;
        border-radius: 50px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Bebas Neue', sans-serif;

        &:hover {
          background-color: var(--accent-green);
          transform: translateY(-3px);
          box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
      }
    }
  }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes drip {
  0% {
    transform: scaleY(0) translateY(-20px);
    opacity: 0;
  }
  20% {
    transform: scaleY(0.3) translateY(-10px);
    opacity: 0.7;
  }
  50% {
    transform: scaleY(1) translateY(0);
    opacity: 1;
  }
  80% {
    transform: scaleY(1.1) translateY(5px);
    opacity: 0.9;
  }
  100% {
    transform: scaleY(0.9) translateY(0);
    opacity: 0.8;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

// Responsive Design
@media (max-width: 768px) {
  .title-large {
    font-size: clamp(2.5rem, 8vw, 4rem);
  }

  .title-medium {
    font-size: clamp(1.5rem, 5vw, 2.5rem);
  }

  .header-section {
    padding: 4rem 0;

    .header-content {
      gap: 2rem;
    }

    .title-with-logo {
      flex-direction: column;
      gap: 1rem;

      .logo-circle {
        width: 60px;
        height: 60px;
      }
    }
  }

  .portrait-image .portrait-placeholder {
    width: 250px !important;
    height: 320px !important;
  }

  .services-section {
    padding: 4rem 0;

    .services-content {
      padding: 0 1rem;
    }

    .service-item {
      padding: 1rem 0;
      gap: 1rem;

      .service-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
      }
    }
  }

  .experience-section {
    padding: 4rem 0;

    .awards-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
    }
  }

  .contact-section {
    padding: 4rem 0;

    .contact-info {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .contact-logo .logo-circle {
      width: 80px;
      height: 80px;
    }
  }

  .custom-cursor {
    display: none;
  }

  body {
    cursor: auto;
  }
}

// Loading Screen
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--background-light), var(--primary-white));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;

  .loader-content {
    text-align: center;

    .loader-logo {
      margin-bottom: 2rem;

      .logo-circle {
        width: 120px;
        height: 120px;
        background-color: var(--accent-green);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        box-shadow: 0 20px 60px rgba(139, 195, 74, 0.3);

        .logo-text {
          font-family: 'Bebas Neue', sans-serif;
          font-size: 2rem;
          color: var(--primary-white);
          font-weight: 700;
          letter-spacing: 0.1em;
        }
      }
    }

    .loader-text {
      margin-bottom: 3rem;

      h2 {
        color: var(--primary-black);
        margin-bottom: 0.5rem;
      }

      p {
        color: var(--text-dark);
      }
    }

    .progress-container {
      width: 300px;
      height: 4px;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      overflow: hidden;
      margin: 0 auto;

      .progress-bar {
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, var(--accent-green), #7cb342);
        border-radius: 2px;
        transform-origin: left;
      }
    }
  }
}

// Performance optimizations
.section {
  will-change: transform;
}

// Smooth transitions for all interactive elements
button, a, .service-item, .award-item {
  will-change: transform;
}
