import { useRef, useEffect } from 'react'
import { useGSAP } from '@gsap/react'
import gsap from 'gsap'

const LoadingScreen = ({ onComplete }) => {
  const loaderRef = useRef()
  const logoRef = useRef()
  const progressRef = useRef()
  const textRef = useRef()

  useGSAP(() => {
    const tl = gsap.timeline({
      onComplete: () => {
        setTimeout(onComplete, 500)
      }
    })

    // Logo animation
    tl.fromTo(logoRef.current,
      { scale: 0, rotation: -180 },
      { scale: 1, rotation: 0, duration: 1, ease: "back.out(1.7)" }
    )

    // Text animation
    .fromTo(textRef.current,
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" }, "-=0.5"
    )

    // Progress bar animation
    .fromTo(progressRef.current,
      { scaleX: 0 },
      { scaleX: 1, duration: 2, ease: "power2.inOut" }, "-=0.3"
    )

    // Loading complete - fade out
    .to(loaderRef.current,
      { opacity: 0, duration: 0.5, ease: "power2.inOut" }, "+=0.5"
    )

  }, { scope: loaderRef })

  return (
    <div ref={loaderRef} className="loading-screen">
      <div className="loader-content">
        <div ref={logoRef} className="loader-logo">
          <div className="logo-circle">
            <div className="logo-text">FRAJ</div>
          </div>
        </div>
        
        <div ref={textRef} className="loader-text">
          <h2 className="title-medium">Loading Portfolio...</h2>
          <p className="body-text">Preparing something amazing</p>
        </div>
        
        <div className="progress-container">
          <div ref={progressRef} className="progress-bar"></div>
        </div>
      </div>
    </div>
  )
}

export default LoadingScreen
