import { useRef } from 'react'
import { useGSAP } from '@gsap/react'
import gsap from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const Experience = () => {
  const experienceRef = useRef()
  const titleRef = useRef()
  const subtitleRef = useRef()
  const awardsRef = useRef()

  const awards = [
    { name: "Awwwards", count: "15x", logo: "🏆" },
    { name: "CSS Design Awards", count: "8x", logo: "🎨" },
    { name: "FWA", count: "12x", logo: "⭐" },
    { name: "Webby Awards", count: "5x", logo: "🌟" },
    { name: "D&AD", count: "3x", logo: "💎" },
    { name: "One Show", count: "4x", logo: "🎯" },
    { name: "Cannes Lions", count: "2x", logo: "🦁" },
    { name: "Red Dot", count: "6x", logo: "🔴" }
  ]

  const clients = [
    "Nike", "Apple", "Google", "Microsoft", "Adobe", "Spotify", 
    "Netflix", "Airbnb", "Uber", "Tesla", "Samsung", "Sony"
  ]

  useGSAP(() => {
    // Main title animation
    gsap.fromTo(titleRef.current,
      { opacity: 0, scale: 0.8 },
      {
        opacity: 1,
        scale: 1,
        duration: 1.2,
        ease: "back.out(1.7)",
        scrollTrigger: {
          trigger: titleRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    )

    // Subtitle animation
    gsap.fromTo(subtitleRef.current,
      { opacity: 0, y: 30 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        delay: 0.3,
        ease: "power2.out",
        scrollTrigger: {
          trigger: subtitleRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    )

    // Awards grid animation
    const awardItems = awardsRef.current.querySelectorAll('.award-item')
    
    awardItems.forEach((item, index) => {
      gsap.fromTo(item,
        { opacity: 0, y: 50, scale: 0.8 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          delay: index * 0.1,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: item,
            start: "top 85%",
            end: "bottom 15%",
            toggleActions: "play none none reverse"
          }
        }
      )

      // Hover effect
      const handleMouseEnter = () => {
        gsap.to(item, {
          scale: 1.05,
          y: -5,
          duration: 0.3,
          ease: "power2.out"
        })
      }

      const handleMouseLeave = () => {
        gsap.to(item, {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        })
      }

      item.addEventListener('mouseenter', handleMouseEnter)
      item.addEventListener('mouseleave', handleMouseLeave)
    })

    // Client logos animation
    const clientItems = experienceRef.current.querySelectorAll('.client-item')
    
    gsap.fromTo(clientItems,
      { opacity: 0, x: -30 },
      {
        opacity: 1,
        x: 0,
        duration: 0.5,
        stagger: 0.05,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ".clients-grid",
          start: "top 85%",
          end: "bottom 15%",
          toggleActions: "play none none reverse"
        }
      }
    )

  }, { scope: experienceRef })

  return (
    <section ref={experienceRef} className="section experience-section">
      <div className="container">
        <div className="experience-content">
          <h2 ref={titleRef} className="title-large experience-title">
            FRAJ HA SCARABOCCHIATO CON
          </h2>

          <div className="experience-stats">
            <div className="stat-item">
              <div className="stat-number title-large">20+</div>
              <div className="stat-label body-text">Years Experience</div>
            </div>
            <div className="stat-item">
              <div className="stat-number title-large">150+</div>
              <div className="stat-label body-text">Projects Completed</div>
            </div>
            <div className="stat-item">
              <div className="stat-number title-large">50+</div>
              <div className="stat-label body-text">Awards Won</div>
            </div>
          </div>

          <div ref={awardsRef} className="awards-grid">
            {awards.map((award, index) => (
              <div key={index} className="award-item">
                <div className="award-logo">{award.logo}</div>
                <div className="award-name title-small">{award.name}</div>
                <div className="award-count body-text">{award.count}</div>
              </div>
            ))}
          </div>

          <div className="clients-section">
            <h3 className="title-medium clients-title">TRUSTED BY</h3>
            <div className="clients-grid">
              {clients.map((client, index) => (
                <div key={index} className="client-item body-text">
                  {client}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Experience
